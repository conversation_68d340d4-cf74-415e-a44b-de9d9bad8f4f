# .env.example
# Database Configuration
DB_NAME=author_platform
DB_USER=postgres
DB_PASSWORD=your_secure_password_here
DB_POOL_SIZE=10

# JWT Configuration
JWT_SECRET=your_base64_encoded_secret_key_at_least_256_bits_long
JWT_EXPIRATION=86400
JWT_REFRESH_EXPIRATION=604800

# Email Configuration
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_app_specific_password

# Application Configuration
APP_BASE_URL=https://jsmith.com
ADMIN_EMAIL=<EMAIL>
CORS_ALLOWED_ORIGINS=https://jsmith.com,https://www.jsmith.com

# Spring Configuration
SPRING_PROFILES_ACTIVE=prod
SERVER_PORT=8080
