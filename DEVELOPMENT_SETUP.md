# Development Setup for shirinov-rustam.com

This setup provides a simple way to debug both backend and frontend code using nginx in Docker as a reverse proxy.

## Prerequisites

1. **Update /etc/hosts file** (you've already done this):
   ```
   127.0.0.1 shirinov-rustam.com
   ```

2. **Backend running on localhost:9090** (you've already done this)

3. **<PERSON>er and Docker Compose installed**

## Quick Start

1. **Start your backend** on localhost:9090 (if not already running)

2. **Run the development environment**:
   ```bash
   ./start-dev.sh
   ```

   Or manually:
   ```bash
   docker-compose -f docker-compose.dev.yml up --build
   ```

3. **Access your application**:
   - Frontend: http://shirinov-rustam.com
   - API calls: http://shirinov-rustam.com/api/* (proxied to localhost:9090)

## How it works

- **Nginx container** serves your frontend and proxies API requests
- **Frontend** is built inside the Docker container and served by nginx
- **Backend** runs on your host machine (localhost:9090)
- **API requests** to `/api/*` are proxied from nginx to your backend

## File Structure

```
nginx/
├── nginx.conf          # Main nginx configuration
├── Dockerfile.dev      # Development Docker setup
└── Dockerfile          # Original production setup

docker-compose.dev.yml  # Development compose file
start-dev.sh           # Convenience startup script
```

## Configuration Details

### Nginx Configuration
- Listens on port 80 for shirinov-rustam.com
- Serves frontend static files from `/usr/share/nginx/html`
- Proxies `/api/*` requests to `host.docker.internal:9090`
- Includes rate limiting and security headers

### Development Benefits
- **Hot reload**: Rebuild the container when you change frontend code
- **Backend debugging**: Your backend runs normally on the host machine
- **Real domain**: Test with shirinov-rustam.com instead of localhost
- **API testing**: All API calls go through the same domain (no CORS issues)

## Stopping the Environment

```bash
docker-compose -f docker-compose.dev.yml down
```

## Troubleshooting

1. **"shirinov-rustam.com not found"**
   - Check your /etc/hosts file has: `127.0.0.1 shirinov-rustam.com`

2. **API calls failing**
   - Ensure your backend is running on localhost:9090
   - Check backend logs for errors

3. **Frontend not updating**
   - Rebuild the container: `docker-compose -f docker-compose.dev.yml up --build`

4. **Port 80 already in use**
   - Stop other web servers (Apache, nginx, etc.)
   - Or change the port in docker-compose.dev.yml: `"8080:80"`
