// config/RateLimitConfig.kt
package com.jsmith.author.config

import io.github.bucket4j.Bandwidth
import io.github.bucket4j.Bucket
import io.github.bucket4j.Refill
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.Duration
import java.util.concurrent.ConcurrentHashMap

@Configuration
class RateLimitConfig {
    
    private val buckets = ConcurrentHashMap<String, Bucket>()
    
    fun resolveBucket(key: String): Bucket {
        return buckets.computeIfAbsent(key) {
            Bucket.builder()
                .addLimit(Bandwidth.classic(100, Refill.intervally(100, Duration.ofMinutes(1))))
                .build()
        }
    }
    
    fun resolveBucketForAnonymous(): Bucket {
        return Bucket.builder()
            .addLimit(Bandwidth.classic(20, Refill.intervally(20, Duration.ofMinutes(1))))
            .build()
    }
}