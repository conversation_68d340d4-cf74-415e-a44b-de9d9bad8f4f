// controller/ContentController.kt
package com.jsmith.author.controller

import com.jsmith.author.dto.*
import com.jsmith.author.service.ContentService
import jakarta.validation.Valid
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.annotation.AuthenticationPrincipal
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.web.bind.annotation.*
import java.util.UUID

@RestController
@RequestMapping("/api")
class ContentController(
    private val contentService: ContentService
) {
    
    // Novel endpoints
    @GetMapping("/novels")
    fun getAllNovels(@PageableDefault(size = 12) pageable: Pageable): Page<NovelDto> {
        return contentService.getAllNovels(pageable)
    }
    
    @GetMapping("/novels/{slug}")
    fun getNovel(@PathVariable slug: String): NovelDto {
        return contentService.getNovelBySlug(slug)
    }
    
    @PostMapping("/novels")
    @PreAuthorize("hasRole('ADMIN')")
    fun createNovel(@Valid @RequestBody request: CreateNovelRequest): ResponseEntity<NovelDto> {
        return ResponseEntity.status(HttpStatus.CREATED).body(contentService.createNovel(request))
    }
    
    // Blog endpoints
    @GetMapping("/blogs")
    fun getAllBlogs(@PageableDefault(size = 12) pageable: Pageable): Page<BlogDto> {
        return contentService.getAllBlogs(pageable)
    }
    
    @GetMapping("/blogs/{slug}")
    fun getBlog(@PathVariable slug: String): BlogDto {
        return contentService.getBlogBySlug(slug)
    }
    
    @PostMapping("/blogs")
    @PreAuthorize("hasRole('ADMIN')")
    fun createBlog(@Valid @RequestBody request: CreateBlogRequest): ResponseEntity<BlogDto> {
        return ResponseEntity.status(HttpStatus.CREATED).body(contentService.createBlog(request))
    }
    
    // Story endpoints
    @GetMapping("/stories")
    fun getAllStories(@PageableDefault(size = 12) pageable: Pageable): Page<StoryDto> {
        return contentService.getAllStories(pageable)
    }
    
    @GetMapping("/stories/{slug}")
    fun getStory(@PathVariable slug: String): StoryDto {
        return contentService.getStoryBySlug(slug)
    }
    
    // Comment endpoints
    @GetMapping("/{contentType}/{contentId}/comments")
    fun getComments(
        @PathVariable contentType: String,
        @PathVariable contentId: UUID
    ): List<CommentDto> {
        return contentService.getCommentsByContentId(contentType, contentId)
    }
    
    @PostMapping("/{contentType}/{contentId}/comments")
    @PreAuthorize("isAuthenticated()")
    fun addComment(
        @PathVariable contentType: String,
        @PathVariable contentId: UUID,
        @Valid @RequestBody request: CreateCommentRequest,
        @AuthenticationPrincipal user: UserDetails
    ): ResponseEntity<CommentDto> {
        // Set content ID based on type
        val updatedRequest = when (contentType.lowercase()) {
            "novels" -> request.copy(novelId = contentId)
            "blogs" -> request.copy(blogId = contentId)
            "stories" -> request.copy(storyId = contentId)
            else -> throw IllegalArgumentException("Invalid content type")
        }
        
        val userId = UUID.fromString(user.username) // Assuming username stores user ID
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(contentService.addComment(updatedRequest, userId))
    }
}