// dto/AuthDtos.kt
package com.jsmith.author.dto

import jakarta.validation.constraints.*
import java.time.LocalDateTime
import java.util.UUID

data class RegisterRequest(
    @field:Email(message = "Invalid email format")
    @field:NotBlank(message = "Email is required")
    val email: String,
    
    @field:NotBlank(message = "Username is required")
    @field:Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    @field:Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "Username can only contain letters, numbers, and underscores")
    val username: String,
    
    @field:NotBlank(message = "Password is required")
    @field:Size(min = 8, message = "Password must be at least 8 characters")
    @field:Pattern(
        regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]+$",
        message = "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
    )
    val password: String,
    
    @field:NotBlank(message = "First name is required")
    val firstName: String,
    
    @field:NotBlank(message = "Last name is required")
    val lastName: String
)

data class LoginRequest(
    @field:NotBlank(message = "Username or email is required")
    val username: String,
    
    @field:NotBlank(message = "Password is required")
    val password: String
)

data class ForgotPasswordRequest(
    @field:Email(message = "Invalid email format")
    @field:NotBlank(message = "Email is required")
    val email: String
)

data class ResetPasswordRequest(
    @field:NotBlank(message = "Token is required")
    val token: String,
    
    @field:NotBlank(message = "New password is required")
    @field:Size(min = 8, message = "Password must be at least 8 characters")
    val newPassword: String
)

data class AuthResponse(
    val token: String,
    val refreshToken: String,
    val user: UserDto
)

data class UserDto(
    val id: UUID,
    val email: String,
    val username: String,
    val firstName: String,
    val lastName: String,
    val role: String,
    val emailVerified: Boolean,
    val createdAt: LocalDateTime
) {
    companion object {
        fun fromEntity(user: com.jsmith.author.entity.User): UserDto {
            return UserDto(
                id = user.id,
                email = user.email,
                username = user.username,
                firstName = user.firstName,
                lastName = user.lastName,
                role = user.role.name,
                emailVerified = user.emailVerified,
                createdAt = user.createdAt
            )
        }
    }
}

data class UpdateUserRequest(
    val firstName: String?,
    val lastName: String?
)
