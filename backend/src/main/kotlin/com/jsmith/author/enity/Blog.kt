// entity/Blog.kt
package com.jsmith.author.entity

import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "blog_posts")
class Blog(
    @Column(nullable = false, length = 200)
    var title: String,
    
    @Column(nullable = false, length = 500)
    var slug: String,
    
    @Column(columnDefinition = "TEXT")
    var excerpt: String,
    
    @Column(columnDefinition = "TEXT")
    var content: String,
    
    @Column
    var featuredImage: String? = null,
    
    @Column
    var publishedDate: LocalDate? = null,
    
    @Column(nullable = false)
    var isPublished: Boolean = false,
    
    @Column(nullable = false)
    var viewCount: Long = 0,
    
    @ElementCollection
    @CollectionTable(name = "blog_tags")
    var tags: MutableSet<String> = mutableSetOf(),
    
    @ElementCollection
    @CollectionTable(name = "blog_categories")
    var categories: MutableSet<String> = mutableSetOf(),
    
    @OneToMany(mappedBy = "blog", cascade = [CascadeType.ALL])
    @OrderBy("createdAt DESC")
    var comments: MutableSet<Comment> = mutableSetOf(),
    
    @Column
    var readingTime: Int? = null // in minutes
) : BaseEntity()
