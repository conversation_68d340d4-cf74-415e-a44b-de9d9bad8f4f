// entity/ContactMessage.kt
package com.jsmith.author.entity

import jakarta.persistence.*
import java.time.LocalDateTime

@Entity
@Table(name = "contact_messages")
class ContactMessage(
    @Column(nullable = false, length = 100)
    var name: String,
    
    @Column(nullable = false, length = 100)
    var email: String,
    
    @Column(length = 200)
    var subject: String? = null,
    
    @Column(columnDefinition = "TEXT", nullable = false)
    var message: String,
    
    @Column(nullable = false)
    var isRead: Boolean = false,
    
    @Column(nullable = false)
    var isReplied: Boolean = false,
    
    @Column
    var repliedAt: LocalDateTime? = null,
    
    @Column
    var ipAddress: String? = null
) : BaseEntity()
