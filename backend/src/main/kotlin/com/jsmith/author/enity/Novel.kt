// entity/Novel.kt
package com.jsmith.author.entity

import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "novels")
class Novel(
    @Column(nullable = false, length = 200)
    var title: String,
    
    @Column(nullable = false, length = 500)
    var slug: String,
    
    @Column(columnDefinition = "TEXT")
    var synopsis: String,
    
    @Column(columnDefinition = "TEXT")
    var content: String,
    
    @Column
    var coverImage: String? = null,
    
    @Column
    var publishedDate: LocalDate? = null,
    
    @Column(nullable = false)
    var isPublished: Boolean = false,
    
    @Column(nullable = false)
    var viewCount: Long = 0,
    
    @ElementCollection
    @CollectionTable(name = "novel_tags")
    var tags: MutableSet<String> = mutableSetOf(),
    
    @OneToMany(mappedBy = "novel", cascade = [CascadeType.ALL])
    @OrderBy("createdAt DESC")
    var comments: MutableSet<Comment> = mutableSetOf(),
    
    @Column
    var readingTime: Int? = null, // in minutes
    
    @Column
    var isbn: String? = null,
    
    @Column
    var purchaseLink: String? = null
) : BaseEntity()