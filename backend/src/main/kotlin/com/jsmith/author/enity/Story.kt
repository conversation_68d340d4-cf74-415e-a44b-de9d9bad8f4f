// entity/Story.kt
package com.jsmith.author.entity

import jakarta.persistence.*
import java.time.LocalDate

@Entity
@Table(name = "short_stories")
class Story(
    @Column(nullable = false, length = 200)
    var title: String,
    
    @Column(nullable = false, length = 500)
    var slug: String,
    
    @Column(columnDefinition = "TEXT")
    var synopsis: String,
    
    @Column(columnDefinition = "TEXT")
    var content: String,
    
    @Column
    var coverImage: String? = null,
    
    @Column
    var publishedDate: LocalDate? = null,
    
    @Column(nullable = false)
    var isPublished: Boolean = false,
    
    @Column(nullable = false)
    var viewCount: Long = 0,
    
    @ElementCollection
    @CollectionTable(name = "story_tags")
    var tags: MutableSet<String> = mutableSetOf(),
    
    @Column
    var genre: String? = null,
    
    @OneToMany(mappedBy = "story", cascade = [CascadeType.ALL])
    @OrderBy("createdAt DESC")
    var comments: MutableSet<Comment> = mutableSetOf(),
    
    @Column
    var readingTime: Int? = null // in minutes
) : BaseEntity()