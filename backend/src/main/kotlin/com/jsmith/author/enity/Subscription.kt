// entity/Subscription.kt
package com.jsmith.author.entity

import jakarta.persistence.*

@Entity
@Table(name = "subscriptions", 
    uniqueConstraints = [
        UniqueConstraint(columnNames = ["user_id", "subscription_type"])
    ]
)
class Subscription(
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    var user: User,
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var subscriptionType: SubscriptionType,
    
    @Column(nullable = false)
    var isActive: Boolean = true,
    
    @Column
    var unsubscribeToken: String? = null
) : BaseEntity()

enum class SubscriptionType {
    NOVELS, BLOGS, STORIES, ALL
}
