// exception/GlobalExceptionHandler.kt
package com.jsmith.author.exception

import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.LockedException
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import java.time.LocalDateTime

@RestControllerAdvice
class GlobalExceptionHandler {
    
    private val logger = LoggerFactory.getLogger(javaClass)
    
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleValidationException(ex: MethodArgumentNotValidException): ResponseEntity<ErrorResponse> {
        val errors = ex.bindingResult.allErrors.associate {
            (it as FieldError).field to (it.defaultMessage ?: "Invalid value")
        }
        
        return ResponseEntity.badRequest().body(
            ErrorResponse(
                error = "Validation Failed",
                message = "Invalid input parameters",
                details = errors,
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(ResourceNotFoundException::class)
    fun handleResourceNotFoundException(ex: ResourceNotFoundException): ResponseEntity<ErrorResponse> {
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(
            ErrorResponse(
                error = "Resource Not Found",
                message = ex.message ?: "The requested resource was not found",
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(BadCredentialsException::class)
    fun handleBadCredentialsException(ex: BadCredentialsException): ResponseEntity<ErrorResponse> {
        return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(
            ErrorResponse(
                error = "Authentication Failed",
                message = "Invalid username or password",
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(LockedException::class)
    fun handleLockedException(ex: LockedException): ResponseEntity<ErrorResponse> {
        return ResponseEntity.status(HttpStatus.LOCKED).body(
            ErrorResponse(
                error = "Account Locked",
                message = "Your account has been temporarily locked due to multiple failed login attempts",
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(DisabledException::class)
    fun handleDisabledException(ex: DisabledException): ResponseEntity<ErrorResponse> {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ErrorResponse(
                error = "Account Disabled",
                message = "Your account is disabled. Please verify your email or contact support",
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(AccessDeniedException::class)
    fun handleAccessDeniedException(ex: AccessDeniedException): ResponseEntity<ErrorResponse> {
        return ResponseEntity.status(HttpStatus.FORBIDDEN).body(
            ErrorResponse(
                error = "Access Denied",
                message = "You don't have permission to access this resource",
                timestamp = LocalDateTime.now()
            )
        )
    }
    
    @ExceptionHandler(Exception::class)
    fun handleGenericException(ex: Exception): ResponseEntity<ErrorResponse> {
        logger.error("Unexpected error occurred", ex)
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
            ErrorResponse(
                error = "Internal Server Error",
                message = "An unexpected error occurred. Please try again later",
                timestamp = LocalDateTime.now()
            )
        )
    }
}

data class ErrorResponse(
    val error: String,
    val message: String,
    val details: Map<String, String>? = null,
    val timestamp: LocalDateTime
)

class ResourceNotFoundException(message: String) : RuntimeException(message)
class EmailAlreadyExistsException(message: String) : RuntimeException(message)
class InvalidTokenException(message: String) : RuntimeException(message)