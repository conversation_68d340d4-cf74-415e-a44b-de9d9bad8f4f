// repository/BlogRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.Blog
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface BlogRepository : JpaRepository<Blog, UUID> {
    fun findBySlug(slug: String): Blog?
    fun findByIsPublishedTrue(pageable: Pageable): Page<Blog>
    fun findByIsPublishedTrueOrderByPublishedDateDesc(pageable: Pageable): Page<Blog>
    
    @Query("SELECT b FROM Blog b WHERE b.isPublished = true AND :category MEMBER OF b.categories")
    fun findByCategory(category: String, pageable: Pageable): Page<Blog>
    
    @Query("SELECT b FROM Blog b WHERE b.isPublished = true AND :tag MEMBER OF b.tags")
    fun findByTag(tag: String, pageable: Pageable): Page<Blog>
    
    @Query("SELECT DISTINCT b.categories FROM Blog b WHERE b.isPublished = true")
    fun findAllCategories(): Set<String>
    
    @Query("SELECT DISTINCT b.tags FROM Blog b WHERE b.isPublished = true")
    fun findAllTags(): Set<String>
    
    fun existsBySlug(slug: String): Boolean
}