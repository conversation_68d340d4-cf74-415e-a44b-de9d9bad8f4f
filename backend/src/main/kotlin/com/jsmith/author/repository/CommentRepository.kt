// repository/CommentRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.Comment
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface CommentRepository : JpaRepository<Comment, UUID> {
    fun findByNovelIdAndParentIsNull(novelId: UUID): List<Comment>
    fun findByBlogIdAndParentIsNull(blogId: UUID): List<Comment>
    fun findByStoryIdAndParentIsNull(storyId: UUID): List<Comment>
    
    fun findByNovelIdAndIsApprovedTrueAndParentIsNull(novelId: UUID): List<Comment>
    fun findByBlogIdAndIsApprovedTrueAndParentIsNull(blogId: UUID): List<Comment>
    fun findByStoryIdAndIsApprovedTrueAndParentIsNull(storyId: UUID): List<Comment>
    
    @Query("SELECT COUNT(c) FROM Comment c WHERE c.isApproved = false")
    fun countPendingComments(): Long
    
    fun findByIsApprovedFalse(): List<Comment>
}
