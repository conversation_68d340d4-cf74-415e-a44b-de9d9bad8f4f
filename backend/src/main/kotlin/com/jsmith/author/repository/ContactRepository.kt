// repository/ContactRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.ContactMessage
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ContactRepository : JpaRepository<ContactMessage, UUID> {
    fun findByIsReadFalse(pageable: Pageable): Page<ContactMessage>
    fun countByIsReadFalse(): Long
    fun findByIsRepliedFalse(pageable: Pageable): Page<ContactMessage>
}