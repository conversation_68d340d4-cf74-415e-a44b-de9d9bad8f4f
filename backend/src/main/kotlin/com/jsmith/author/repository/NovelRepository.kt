// repository/NovelRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.Novel
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface NovelRepository : JpaRepository<Novel, UUID> {
    fun findBySlug(slug: String): Novel?
    fun findByIsPublishedTrue(pageable: Pageable): Page<Novel>
    fun findByIsPublishedTrueOrderByPublishedDateDesc(pageable: Pageable): Page<Novel>
    
    @Query("SELECT n FROM Novel n WHERE n.isPublished = true AND :tag MEMBER OF n.tags")
    fun findByTag(tag: String, pageable: Pageable): Page<Novel>
    
    @Query("SELECT DISTINCT n.tags FROM Novel n WHERE n.isPublished = true")
    fun findAllTags(): Set<String>
    
    fun existsBySlug(slug: String): Boolean
}