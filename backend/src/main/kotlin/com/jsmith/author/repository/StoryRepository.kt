// repository/StoryRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.Story
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface StoryRepository : JpaRepository<Story, UUID> {
    fun findBySlug(slug: String): Story?
    fun findByIsPublishedTrue(pageable: Pageable): Page<Story>
    fun findByIsPublishedTrueOrderByPublishedDateDesc(pageable: Pageable): Page<Story>
    fun findByGenre(genre: String, pageable: Pageable): Page<Story>
    
    @Query("SELECT s FROM Story s WHERE s.isPublished = true AND :tag MEMBER OF s.tags")
    fun findByTag(tag: String, pageable: Pageable): Page<Story>
    
    @Query("SELECT DISTINCT s.genre FROM Story s WHERE s.isPublished = true AND s.genre IS NOT NULL")
    fun findAllGenres(): Set<String>
    
    @Query("SELECT DISTINCT s.tags FROM Story s WHERE s.isPublished = true")
    fun findAllTags(): Set<String>
    
    fun existsBySlug(slug: String): Boolean
}