// repository/SubscriptionRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.Subscription
import com.jsmith.author.entity.SubscriptionType
import com.jsmith.author.entity.User
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface SubscriptionRepository : JpaRepository<Subscription, UUID> {
    fun findByUserAndSubscriptionType(user: User, type: SubscriptionType): Subscription?
    fun findBySubscriptionTypeAndIsActiveTrue(type: SubscriptionType): List<Subscription>
    fun findByUnsubscribeToken(token: String): Subscription?
    fun existsByUserAndSubscriptionType(user: User, type: SubscriptionType): Boolean
    fun findByUser(user: User): List<Subscription>
}
