// repository/UserRepository.kt
package com.jsmith.author.repository

import com.jsmith.author.entity.User
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface UserRepository : JpaRepository<User, UUID> {
    fun findByEmail(email: String): User?
    fun findByUsername(username: String): User?
    fun findByUsernameOrEmail(username: String, email: String): User?
    fun findByEmailVerificationToken(token: String): User?
    fun findByPasswordResetToken(token: String): User?
    fun existsByEmail(email: String): Boolean
    fun existsByUsername(username: String): <PERSON>olean
    
    @Query("SELECT u FROM User u WHERE u.accountLockedUntil < CURRENT_TIMESTAMP AND u.accountLockedUntil IS NOT NULL")
    fun findUsersToUnlock(): List<User>
}
