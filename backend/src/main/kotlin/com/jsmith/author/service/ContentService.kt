// service/ContentService.kt
package com.jsmith.author.service

import com.jsmith.author.dto.*
import com.jsmith.author.entity.*
import com.jsmith.author.exception.ResourceNotFoundException
import com.jsmith.author.repository.*
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
@Transactional
class ContentService(
    private val novelRepository: NovelRepository,
    private val blogRepository: BlogRepository,
    private val storyRepository: StoryRepository,
    private val commentRepository: CommentRepository,
    private val userService: UserService,
    private val emailService: EmailService
) {
    
    // Novel operations
    @Cacheable("novels")
    fun getAllNovels(pageable: Pageable): Page<NovelDto> {
        return novelRepository.findByIsPublishedTrue(pageable)
            .map { NovelDto.fromEntity(it) }
    }
    
    @Cacheable("novel", key = "#slug")
    fun getNovelBySlug(slug: String): NovelDto {
        val novel = novelRepository.findBySlug(slug)
            ?: throw ResourceNotFoundException("Novel not found")
        
        // Increment view count
        novel.viewCount++
        novelRepository.save(novel)
        
        return NovelDto.fromEntity(novel)
    }
    
    @CacheEvict("novels", allEntries = true)
    fun createNovel(request: CreateNovelRequest): NovelDto {
        val novel = Novel(
            title = request.title,
            slug = generateSlug(request.title),
            synopsis = request.synopsis,
            content = request.content,
            coverImage = request.coverImage,
            publishedDate = request.publishedDate,
            isPublished = request.isPublished,
            tags = request.tags.toMutableSet(),
            readingTime = calculateReadingTime(request.content),
            isbn = request.isbn,
            purchaseLink = request.purchaseLink
        )
        
        return NovelDto.fromEntity(novelRepository.save(novel))
    }
    
    // Blog operations
    @Cacheable("blogs")
    fun getAllBlogs(pageable: Pageable): Page<BlogDto> {
        return blogRepository.findByIsPublishedTrue(pageable)
            .map { BlogDto.fromEntity(it) }
    }
    
    @Cacheable("blog", key = "#slug")
    fun getBlogBySlug(slug: String): BlogDto {
        val blog = blogRepository.findBySlug(slug)
            ?: throw ResourceNotFoundException("Blog post not found")
        
        blog.viewCount++
        blogRepository.save(blog)
        
        return BlogDto.fromEntity(blog)
    }
    
    @CacheEvict("blogs", allEntries = true)
    fun createBlog(request: CreateBlogRequest): BlogDto {
        val blog = Blog(
            title = request.title,
            slug = generateSlug(request.title),
            excerpt = request.excerpt,
            content = request.content,
            featuredImage = request.featuredImage,
            publishedDate = request.publishedDate,
            isPublished = request.isPublished,
            tags = request.tags.toMutableSet(),
            categories = request.categories.toMutableSet(),
            readingTime = calculateReadingTime(request.content)
        )
        
        val savedBlog = blogRepository.save(blog)
        
        // Notify subscribers
        if (savedBlog.isPublished) {
            emailService.notifySubscribers(SubscriptionType.BLOGS, savedBlog)
        }
        
        return BlogDto.fromEntity(savedBlog)
    }
    
    // Story operations
    @Cacheable("stories")
    fun getAllStories(pageable: Pageable): Page<StoryDto> {
        return storyRepository.findByIsPublishedTrue(pageable)
            .map { StoryDto.fromEntity(it) }
    }
    
    @Cacheable("story", key = "#slug")
    fun getStoryBySlug(slug: String): StoryDto {
        val story = storyRepository.findBySlug(slug)
            ?: throw ResourceNotFoundException("Story not found")
        
        story.viewCount++
        storyRepository.save(story)
        
        return StoryDto.fromEntity(story)
    }
    
    // Comment operations
    fun addComment(request: CreateCommentRequest, userId: UUID): CommentDto {
        val user = userService.findById(userId)
        
        val comment = Comment(
            content = request.content,
            user = user,
            novel = request.novelId?.let { 
                novelRepository.findById(it).orElse(null) 
            },
            blog = request.blogId?.let { 
                blogRepository.findById(it).orElse(null) 
            },
            story = request.storyId?.let { 
                storyRepository.findById(it).orElse(null) 
            },
            parent = request.parentId?.let { 
                commentRepository.findById(it).orElse(null) 
            }
        )
        
        val savedComment = commentRepository.save(comment)
        
        // Notify author about new comment
        emailService.notifyNewComment(savedComment)
        
        return CommentDto.fromEntity(savedComment)
    }
    
    fun getCommentsByContentId(contentType: String, contentId: UUID): List<CommentDto> {
        val comments = when (contentType.lowercase()) {
            "novel" -> commentRepository.findByNovelIdAndParentIsNull(contentId)
            "blog" -> commentRepository.findByBlogIdAndParentIsNull(contentId)
            "story" -> commentRepository.findByStoryIdAndParentIsNull(contentId)
            else -> throw IllegalArgumentException("Invalid content type")
        }
        
        return comments.map { CommentDto.fromEntity(it) }
    }
    
    private fun generateSlug(title: String): String {
        return title.lowercase()
            .replace(Regex("[^a-z0-9\\s-]"), "")
            .replace(Regex("\\s+"), "-")
            .trim('-')
    }
    
    private fun calculateReadingTime(content: String): Int {
        val wordsPerMinute = 200
        val wordCount = content.split(Regex("\\s+")).size
        return (wordCount / wordsPerMinute).coerceAtLeast(1)
    }
}
