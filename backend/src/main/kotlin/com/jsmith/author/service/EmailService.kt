// service/EmailService.kt
package com.jsmith.author.service

import com.jsmith.author.entity.*

interface EmailService {
    
    /**
     * Send email verification to user
     */
    fun sendVerificationEmail(user: User)
    
    /**
     * Send password reset email to user
     */
    fun sendPasswordResetEmail(user: User)
    
    /**
     * Notify subscribers about new content
     */
    fun notifySubscribers(type: SubscriptionType, content: Any)
    
    /**
     * Notify admin about new comment
     */
    fun notifyNewComment(comment: Comment)
    
    /**
     * Notify admin about new contact message
     */
    fun notifyContactMessage(contactMessage: ContactMessage)
}
