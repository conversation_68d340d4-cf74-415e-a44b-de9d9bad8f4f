// service/MockEmailService.kt
package com.jsmith.author.service

import com.jsmith.author.entity.*
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

@Service
@Primary
@ConditionalOnProperty(
    name = ["app.email.service"],
    havingValue = "mock"
)
class MockEmailService : EmailService {
    
    private val logger = LoggerFactory.getLogger(MockEmailService::class.java)
    
    @Async
    override fun sendVerificationEmail(user: User) {
        logger.info("MOCK EMAIL: Verification email would be sent to ${user.email}")
        logger.info("  - Recipient: ${user.firstName} ${user.lastName}")
        logger.info("  - Subject: Verify Your Email - J. Smith Author Platform")
        logger.info("  - Verification Token: ${user.emailVerificationToken}")
        logger.info("  - Template: email-verification")
    }
    
    @Async
    override fun sendPasswordResetEmail(user: User) {
        logger.info("MOCK EMAIL: Password reset email would be sent to ${user.email}")
        logger.info("  - Recipient: ${user.firstName} ${user.lastName}")
        logger.info("  - Subject: Password Reset Request")
        logger.info("  - Reset Token: ${user.passwordResetToken}")
        logger.info("  - Template: password-reset")
    }
    
    @Async
    override fun notifySubscribers(type: SubscriptionType, content: Any) {
        logger.info("MOCK EMAIL: Subscriber notification would be sent")
        logger.info("  - Subscription Type: $type")
        logger.info("  - Content Type: ${content::class.simpleName}")
        
        val subject = when (content) {
            is Novel -> "New Novel: ${content.title}"
            is Blog -> "New Blog Post: ${content.title}"
            is Story -> "New Short Story: ${content.title}"
            else -> "New Content Available"
        }
        
        logger.info("  - Subject: $subject")
        logger.info("  - Template: new-content")
        logger.info("  - Note: In production, this would be sent to all active subscribers of type $type")
    }
    
    @Async
    override fun notifyNewComment(comment: Comment) {
        logger.info("MOCK EMAIL: New comment notification would be sent to admin")
        logger.info("  - Comment Author: ${comment.user.username}")
        logger.info("  - Comment Content: ${comment.content.take(100)}${if (comment.content.length > 100) "..." else ""}")
        
        val contentTitle = when {
            comment.novel != null -> comment.novel!!.title
            comment.blog != null -> comment.blog!!.title
            comment.story != null -> comment.story!!.title
            else -> "Unknown"
        }
        
        logger.info("  - Content Title: $contentTitle")
        logger.info("  - Subject: New Comment Received")
        logger.info("  - Template: new-comment")
    }
    
    @Async
    override fun notifyContactMessage(contactMessage: ContactMessage) {
        logger.info("MOCK EMAIL: Contact message notification would be sent to admin")
        logger.info("  - From: ${contactMessage.name} <${contactMessage.email}>")
        logger.info("  - Subject: New Contact Message: ${contactMessage.subject ?: "No Subject"}")
        logger.info("  - Message: ${contactMessage.message.take(100)}${if (contactMessage.message.length > 100) "..." else ""}")
        logger.info("  - IP Address: ${contactMessage.ipAddress ?: "Unknown"}")
        logger.info("  - Template: contact-message")
    }
}
