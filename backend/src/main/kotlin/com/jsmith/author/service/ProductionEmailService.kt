// service/ProductionEmailService.kt
package com.jsmith.author.service

import com.jsmith.author.entity.*
import com.jsmith.author.repository.SubscriptionRepository
import jakarta.mail.internet.MimeMessage
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Primary
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.MimeMessageHelper
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.thymeleaf.TemplateEngine
import org.thymeleaf.context.Context

@Service
@Primary
@ConditionalOnProperty(
    name = ["app.email.service"],
    havingValue = "production",
    matchIfMissing = true
)
class ProductionEmailService(
    private val mailSender: JavaMailSender,
    private val templateEngine: TemplateEngine,
    private val subscriptionRepository: SubscriptionRepository,
    @Value("\${app.base-url}") private val baseUrl: String,
    @Value("\${spring.mail.username}") private val fromEmail: String
) : EmailService {
    
    @Async
    override fun sendVerificationEmail(user: User) {
        val context = Context().apply {
            setVariable("name", "${user.firstName} ${user.lastName}")
            setVariable("verificationUrl", "$baseUrl/verify-email?token=${user.emailVerificationToken}")
        }
        
        sendEmail(
            to = user.email,
            subject = "Verify Your Email - J. Smith Author Platform",
            template = "email-verification",
            context = context
        )
    }
    
    @Async
    override fun sendPasswordResetEmail(user: User) {
        val context = Context().apply {
            setVariable("name", "${user.firstName} ${user.lastName}")
            setVariable("resetUrl", "$baseUrl/reset-password?token=${user.passwordResetToken}")
        }
        
        sendEmail(
            to = user.email,
            subject = "Password Reset Request",
            template = "password-reset",
            context = context
        )
    }
    
    @Async
    override fun notifySubscribers(type: SubscriptionType, content: Any) {
        val subscriptions = subscriptionRepository.findBySubscriptionTypeAndIsActiveTrue(type)
        
        subscriptions.forEach { subscription ->
            val context = Context().apply {
                setVariable("name", "${subscription.user.firstName}")
                setVariable("content", content)
                setVariable("unsubscribeUrl", "$baseUrl/unsubscribe?token=${subscription.unsubscribeToken}")
            }
            
            val subject = when (content) {
                is Novel -> "New Novel: ${content.title}"
                is Blog -> "New Blog Post: ${content.title}"
                is Story -> "New Short Story: ${content.title}"
                else -> "New Content Available"
            }
            
            sendEmail(
                to = subscription.user.email,
                subject = subject,
                template = "new-content",
                context = context
            )
        }
    }
    
    @Async
    override fun notifyNewComment(comment: Comment) {
        val context = Context().apply {
            setVariable("commentAuthor", comment.user.username)
            setVariable("commentContent", comment.content)
            setVariable("contentTitle", when {
                comment.novel != null -> comment.novel!!.title
                comment.blog != null -> comment.blog!!.title
                comment.story != null -> comment.story!!.title
                else -> "Unknown"
            })
        }

        sendEmail(
            to = fromEmail, // Send to admin
            subject = "New Comment Received",
            template = "new-comment",
            context = context
        )
    }

    @Async
    override fun notifyContactMessage(contactMessage: ContactMessage) {
        val context = Context().apply {
            setVariable("name", contactMessage.name)
            setVariable("email", contactMessage.email)
            setVariable("subject", contactMessage.subject ?: "No Subject")
            setVariable("message", contactMessage.message)
            setVariable("ipAddress", contactMessage.ipAddress ?: "Unknown")
        }

        sendEmail(
            to = fromEmail, // Send to admin
            subject = "New Contact Message: ${contactMessage.subject ?: "No Subject"}",
            template = "contact-message",
            context = context
        )
    }
    
    private fun sendEmail(to: String, subject: String, template: String, context: Context) {
        try {
            val message: MimeMessage = mailSender.createMimeMessage()
            val helper = MimeMessageHelper(message, true, "UTF-8")
            
            helper.setFrom(fromEmail, "J. Smith Author Platform")
            helper.setTo(to)
            helper.setSubject(subject)
            
            val htmlContent = templateEngine.process(template, context)
            helper.setText(htmlContent, true)
            
            mailSender.send(message)
        } catch (e: Exception) {
            // Log error but don't throw to prevent blocking main flow
            println("Failed to send email: ${e.message}")
        }
    }
}
