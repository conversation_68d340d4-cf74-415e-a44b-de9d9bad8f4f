-- V2__create_content.sql
CREATE TABLE novels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(500) NOT NULL UNIQUE,
    synopsis TEXT,
    content TEXT,
    cover_image VARCHAR(500),
    published_date DATE,
    is_published BOOLEAN NOT NULL DEFAULT false,
    view_count BIGINT DEFAULT 0,
    reading_time INT,
    isbn VARCHAR(50),
    purchase_link VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

CREATE TABLE novel_tags (
    novel_id UUID NOT NULL REFERENCES novels(id) ON DELETE CASCADE,
    tags VARCHAR(100) NOT NULL
);

CREATE TABLE blog_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(500) NOT NULL UNIQUE,
    excerpt TEXT,
    content TEXT,
    featured_image VARCHAR(500),
    published_date DATE,
    is_published BOOLEAN NOT NULL DEFAULT false,
    view_count BIGINT DEFAULT 0,
    reading_time INT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

CREATE TABLE blog_tags (
    blog_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
    tags VARCHAR(100) NOT NULL
);

CREATE TABLE blog_categories (
    blog_id UUID NOT NULL REFERENCES blog_posts(id) ON DELETE CASCADE,
    categories VARCHAR(100) NOT NULL
);

CREATE TABLE short_stories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    slug VARCHAR(500) NOT NULL UNIQUE,
    synopsis TEXT,
    content TEXT,
    cover_image VARCHAR(500),
    published_date DATE,
    is_published BOOLEAN NOT NULL DEFAULT false,
    view_count BIGINT DEFAULT 0,
    genre VARCHAR(100),
    reading_time INT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

CREATE TABLE story_tags (
    story_id UUID NOT NULL REFERENCES short_stories(id) ON DELETE CASCADE,
    tags VARCHAR(100) NOT NULL
);

CREATE INDEX idx_novel_slug ON novels(slug);
CREATE INDEX idx_novel_published ON novels(is_published, published_date);
CREATE INDEX idx_blog_slug ON blog_posts(slug);
CREATE INDEX idx_blog_published ON blog_posts(is_published, published_date);
CREATE INDEX idx_story_slug ON short_stories(slug);
CREATE INDEX idx_story_published ON short_stories(is_published, published_date);