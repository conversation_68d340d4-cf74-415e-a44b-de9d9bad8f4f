-- V3__create_comments_and_subscriptions.sql
CREATE TABLE comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id),
    novel_id UUID REFERENCES novels(id) ON DELETE CASCADE,
    blog_id UUID REFERENCES blog_posts(id) ON DELETE CASCADE,
    story_id UUID REFERENCES short_stories(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES comments(id) ON DELETE CASCADE,
    is_approved BOOLEAN NOT NULL DEFAULT true,
    is_deleted BOOLEAN NOT NULL DEFAULT false,
    edited_at TIMESTAMP,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    CHECK (
        (novel_id IS NOT NULL AND blog_id IS NULL AND story_id IS NULL) OR
        (novel_id IS NULL AND blog_id IS NOT NULL AND story_id IS NULL) OR
        (novel_id IS NULL AND blog_id IS NULL AND story_id IS NOT NULL)
    )
);

CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_type VARCHAR(50) NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    unsubscribe_token VARCHAR(255),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0,
    UNIQUE(user_id, subscription_type)
);

CREATE TABLE contact_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(200),
    message TEXT NOT NULL,
    is_read BOOLEAN NOT NULL DEFAULT false,
    is_replied BOOLEAN NOT NULL DEFAULT false,
    replied_at TIMESTAMP,
    ip_address VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    version BIGINT DEFAULT 0
);

CREATE INDEX idx_comment_user ON comments(user_id);
CREATE INDEX idx_comment_novel ON comments(novel_id);
CREATE INDEX idx_comment_blog ON comments(blog_id);
CREATE INDEX idx_comment_story ON comments(story_id);
CREATE INDEX idx_comment_parent ON comments(parent_id);
CREATE INDEX idx_comment_approved ON comments(is_approved);
CREATE INDEX idx_subscription_user ON subscriptions(user_id);
CREATE INDEX idx_subscription_type ON subscriptions(subscription_type);
CREATE INDEX idx_contact_read ON contact_messages(is_read);