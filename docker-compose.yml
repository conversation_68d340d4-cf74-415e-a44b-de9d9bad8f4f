version: '3.9'

services:
  postgres:
    image: postgres:16-alpine
    container_name: jsmith-postgres
    environment:
      POSTGRES_DB: ${DB_NAME:-author_platform}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - jsmith-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5


  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: jsmith-backend
    environment:
      SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-dev}
      DB_URL: *******************************/${DB_NAME:-author_platform}
      DB_USERNAME: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}

      JWT_SECRET: ${JWT_SECRET}
      MAIL_HOST: ${MAIL_HOST}
      MAIL_PORT: ${MAIL_PORT}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      APP_BASE_URL: ${APP_BASE_URL:-http://localhost:3000}
    ports:
      - "9090:8080"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./logs:/app/logs
    networks:
      - jsmith-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  nginx:
    build:
      context: .
      dockerfile: nginx/Dockerfile.dev
    container_name: jsmith-nginx
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - jsmith-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

networks:
  jsmith-network:
    driver: bridge

volumes:
  postgres_data:
