// src/App.tsx
import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import Layout from '@components/Layout';
import LoadingSpinner from '@components/LoadingSpinner';
import ProtectedRoute from '@components/ProtectedRoute';
import ScrollToTop from '@components/ScrollToTop';
import { useAuthStore } from '@store/authStore';

// Lazy load pages for code splitting
const Home = lazy(() => import('@pages/Home'));
const Novels = lazy(() => import('@pages/Novels'));
const NovelDetail = lazy(() => import('@pages/NovelDetail'));
const Blogs = lazy(() => import('@pages/Blogs'));
const BlogDetail = lazy(() => import('@pages/BlogDetail'));
const Stories = lazy(() => import('@pages/Stories'));
const StoryDetail = lazy(() => import('@pages/StoryDetail'));
const Contact = lazy(() => import('@pages/Contact'));
const Login = lazy(() => import('@pages/Login'));
const Register = lazy(() => import('@pages/Register'));
const ForgotPassword = lazy(() => import('@pages/ForgotPassword'));
const ResetPassword = lazy(() => import('@pages/ResetPassword'));
const EmailVerification = lazy(() => import('@pages/EmailVerification'));
const Profile = lazy(() => import('@pages/Profile'));
const Subscription = lazy(() => import('@pages/Subscription'));
const TermsAndConditions = lazy(() => import('@pages/TermsAndConditions'));
const NotFound = lazy(() => import('@pages/NotFound'));

const App: React.FC = () => {
  const { isAuthenticated, initialize } = useAuthStore();

  React.useEffect(() => {
    initialize();
  }, [initialize]);

  return (
    <>
      <ScrollToTop />
      <AnimatePresence mode="wait">
        <Suspense fallback={<LoadingSpinner fullScreen />}>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<Home />} />
              
              {/* Content Routes */}
              <Route path="novels" element={<Novels />} />
              <Route path="novels/:slug" element={<NovelDetail />} />
              
              <Route path="blog" element={<Blogs />} />
              <Route path="blog/:slug" element={<BlogDetail />} />
              
              <Route path="stories" element={<Stories />} />
              <Route path="stories/:slug" element={<StoryDetail />} />
              
              <Route path="contact" element={<Contact />} />
              
              {/* Auth Routes */}
              <Route path="login" element={
                isAuthenticated ? <Navigate to="/profile" /> : <Login />
              } />
              <Route path="register" element={
                isAuthenticated ? <Navigate to="/profile" /> : <Register />
              } />
              <Route path="forgot-password" element={<ForgotPassword />} />
              <Route path="reset-password" element={<ResetPassword />} />
              <Route path="verify-email" element={<EmailVerification />} />
              
              {/* Protected Routes */}
              <Route element={<ProtectedRoute />}>
                <Route path="profile" element={<Profile />} />
                <Route path="subscription" element={<Subscription />} />
              </Route>
              
              {/* Legal */}
              <Route path="terms" element={<TermsAndConditions />} />
              
              {/* 404 */}
              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
        </Suspense>
      </AnimatePresence>
    </>
  );
};

export default App;