import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Clock, Eye, MessageCircle, Calendar } from 'lucide-react';
import { format } from 'date-fns';

interface ContentCardProps {
  title: string;
  slug: string;
  excerpt: string;
  image?: string;
  type: 'novel' | 'blog' | 'story';
  publishedDate?: string;
  readingTime?: number;
  viewCount: number;
  commentCount: number;
  tags?: string[];
}

const ContentCard: React.FC<ContentCardProps> = ({
  title,
  slug,
  excerpt,
  image,
  type,
  publishedDate,
  readingTime,
  viewCount,
  commentCount,
  tags = [],
}) => {
  const getPath = () => {
    switch (type) {
      case 'novel':
        return `/novels/${slug}`;
      case 'blog':
        return `/blog/${slug}`;
      case 'story':
        return `/stories/${slug}`;
    }
  };

  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      whileHover={{ y: -5 }}
      className="card group cursor-pointer"
    >
      <Link to={getPath()}>
        {image && (
          <div className="relative h-48 -mx-6 -mt-6 mb-6 overflow-hidden rounded-t-xl">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-dark-bg/60 to-transparent" />
          </div>
        )}
        
        <h3 className="text-xl font-serif text-dark-text group-hover:text-accent-gold transition-colors mb-3">
          {title}
        </h3>
        
        <p className="text-dark-muted line-clamp-3 mb-4">{excerpt}</p>
        
        <div className="flex items-center justify-between text-xs text-dark-muted">
          <div className="flex items-center space-x-4">
            {publishedDate && (
              <span className="flex items-center space-x-1">
                <Calendar className="w-3 h-3" />
                <span>{format(new Date(publishedDate), 'MMM d, yyyy')}</span>
              </span>
            )}
            {readingTime && (
              <span className="flex items-center space-x-1">
                <Clock className="w-3 h-3" />
                <span>{readingTime} min</span>
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-3">
            <span className="flex items-center space-x-1">
              <Eye className="w-3 h-3" />
              <span>{viewCount}</span>
            </span>
            <span className="flex items-center space-x-1">
              <MessageCircle className="w-3 h-3" />
              <span>{commentCount}</span>
            </span>
          </div>
        </div>
        
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-4">
            {tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 text-xs bg-dark-bg rounded-full border border-dark-border"
              >
                {tag}
              </span>
            ))}
          </div>
        )}
      </Link>
    </motion.article>
  );
};

export default ContentCard;