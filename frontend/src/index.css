// src/index.css
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Merriweather:wght@300;400;700&family=Fira+Code:wght@400;500&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply scroll-smooth;
  }
  
  body {
    @apply bg-dark-bg text-dark-text antialiased;
  }
  
  ::selection {
    @apply bg-primary-600 text-white;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-dark-surface;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-dark-border hover:bg-primary-700 rounded-full;
  }
  
  /* Loading animation */
  .loading-dots span {
    animation: loading 1.4s infinite ease-in-out both;
  }
  
  .loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  .loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }
  
  @keyframes loading {
    0%, 80%, 100% {
      transform: scale(0);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply bg-dark-surface hover:bg-dark-border text-dark-text font-medium py-2 px-4 rounded-lg border border-dark-border transition-colors duration-200;
  }
  
  .input-field {
    @apply bg-dark-surface border border-dark-border rounded-lg px-4 py-2 text-dark-text placeholder-dark-muted focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .card {
    @apply bg-dark-surface border border-dark-border rounded-xl p-6 hover:border-primary-700 transition-all duration-300;
  }
  
  .prose-dark {
    @apply prose prose-dark max-w-none;
  }
}
