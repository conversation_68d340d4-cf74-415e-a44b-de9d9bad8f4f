// src/services/contentService.ts
import { apiClient } from './api';
import { Novel, Blog, Story, Comment, PaginatedResponse } from '@types';

export const contentService = {
  // Novels
  async getNovels(page = 0, size = 12): Promise<PaginatedResponse<Novel>> {
    const response = await apiClient.get<PaginatedResponse<Novel>>('/novels', {
      params: { page, size },
    });
    return response.data;
  },

  async getNovel(slug: string): Promise<Novel> {
    const response = await apiClient.get<Novel>(`/novels/${slug}`);
    return response.data;
  },

  async getNovelComments(id: string): Promise<Comment[]> {
    const response = await apiClient.get<Comment[]>(`/novels/${id}/comments`);
    return response.data;
  },

  // Blogs
  async getBlogs(page = 0, size = 12): Promise<PaginatedResponse<Blog>> {
    const response = await apiClient.get<PaginatedResponse<Blog>>('/blogs', {
      params: { page, size },
    });
    return response.data;
  },

  async getBlog(slug: string): Promise<Blog> {
    const response = await apiClient.get<Blog>(`/blogs/${slug}`);
    return response.data;
  },

  async getBlogComments(id: string): Promise<Comment[]> {
    const response = await apiClient.get<Comment[]>(`/blogs/${id}/comments`);
    return response.data;
  },

  // Stories
  async getStories(page = 0, size = 12): Promise<PaginatedResponse<Story>> {
    const response = await apiClient.get<PaginatedResponse<Story>>('/stories', {
      params: { page, size },
    });
    return response.data;
  },

  async getStory(slug: string): Promise<Story> {
    const response = await apiClient.get<Story>(`/stories/${slug}`);
    return response.data;
  },

  async getStoryComments(id: string): Promise<Comment[]> {
    const response = await apiClient.get<Comment[]>(`/stories/${id}/comments`);
    return response.data;
  },

  // Comments
  async addComment(
    contentType: 'novels' | 'blogs' | 'stories',
    contentId: string,
    content: string,
    parentId?: string
  ): Promise<Comment> {
    const response = await apiClient.post<Comment>(`/${contentType}/${contentId}/comments`, {
      content,
      parentId,
    });
    return response.data;
  },

  async deleteComment(id: string): Promise<void> {
    await apiClient.delete(`/comments/${id}`);
  },

  async updateComment(id: string, content: string): Promise<Comment> {
    const response = await apiClient.put<Comment>(`/comments/${id}`, { content });
    return response.data;
  },
};