// src/services/subscriptionService.ts
import { apiClient } from './api';
import { SubscriptionType } from '@types';

export const subscriptionService = {
  async getSubscriptions(): Promise<SubscriptionType[]> {
    const response = await apiClient.get<SubscriptionType[]>('/subscriptions');
    return response.data;
  },

  async subscribe(type: SubscriptionType['type']): Promise<void> {
    await apiClient.post('/subscriptions', { type });
  },

  async unsubscribe(type: SubscriptionType['type']): Promise<void> {
    await apiClient.delete(`/subscriptions/${type}`);
  },

  async unsubscribeByToken(token: string): Promise<void> {
    await apiClient.post('/subscriptions/unsubscribe', { token });
  },
};