// src/types/index.ts
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: 'USER' | 'MODERATOR' | 'ADMIN';
  emailVerified: boolean;
  createdAt: string;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export interface Novel {
  id: string;
  title: string;
  slug: string;
  synopsis: string;
  content: string;
  coverImage?: string;
  publishedDate?: string;
  viewCount: number;
  tags: string[];
  readingTime?: number;
  isbn?: string;
  purchaseLink?: string;
  commentCount: number;
  createdAt: string;
}

export interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage?: string;
  publishedDate?: string;
  viewCount: number;
  tags: string[];
  categories: string[];
  readingTime?: number;
  commentCount: number;
  createdAt: string;
}

export interface Story {
  id: string;
  title: string;
  slug: string;
  synopsis: string;
  content: string;
  coverImage?: string;
  publishedDate?: string;
  viewCount: number;
  tags: string[];
  genre?: string;
  readingTime?: number;
  commentCount: number;
  createdAt: string;
}

export interface Comment {
  id: string;
  content: string;
  author: string;
  createdAt: string;
  editedAt?: string;
  replies: Comment[];
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

export interface ContactForm {
  name: string;
  email: string;
  subject?: string;
  message: string;
}

export interface SubscriptionType {
  type: 'NOVELS' | 'BLOGS' | 'STORIES' | 'ALL';
  isActive: boolean;
}