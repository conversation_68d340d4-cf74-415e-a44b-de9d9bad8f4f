# nginx/Dockerfile
FROM nginx:alpine

# Install certbot for SSL certificates
RUN apk add --no-cache certbot certbot-nginx openssl

# Remove default config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf
COPY conf.d/ /etc/nginx/conf.d/

# Create directory for SSL certificates
RUN mkdir -p /etc/letsencrypt/live/jsmith.com

# Generate self-signed certificate for development
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/letsencrypt/live/jsmith.com/privkey.pem \
    -out /etc/letsencrypt/live/jsmith.com/fullchain.pem \
    -subj "/C=US/ST=State/L=City/O=JSmith/CN=jsmith.com"

# Create dhparam file for better SSL security
RUN openssl dhparam -out /etc/nginx/dhparam.pem 2048

EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]