# nginx/Dockerfile
FROM nginx:alpine

# Remove default config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Create html directory for frontend files
RUN mkdir -p /usr/share/nginx/html

# Create a simple index.html as placeholder
RUN echo '<html><body><h1>Frontend will be served here</h1><p>Make sure to build and copy your frontend files to this container</p></body></html>' > /usr/share/nginx/html/index.html

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
