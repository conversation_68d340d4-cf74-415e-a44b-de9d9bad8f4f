#!/bin/bash

echo "Starting development environment for shirinov-rustam.com"
echo ""

# Check if /etc/hosts has the entry
if ! grep -q "shirinov-rustam.com" /etc/hosts; then
    echo " Warning: shirinov-rustam.com not found in /etc/hosts"
    echo "   Please add this line to your /etc/hosts file:"
    echo "   127.0.0.1 shirinov-rustam.com"
    echo ""
fi

# Check if backend is running on port 9090
if ! nc -z localhost 9090 2>/dev/null; then
    echo  "Warning: Backend not detected on localhost:9090"
    echo "   Please make sure your backend is running on port 9090"
    echo ""
fi

echo " Building and starting nginx container..."
docker compose -f docker-compose.dev.yml up --build

echo ""
echo " Development environment started!"
echo ""
echo " Access your application at: http://shirinov-rustam.com"
echo " Backend API will be proxied from localhost:9090 to http://shirinov-rustam.com/api"
echo ""
echo "To stop: docker-compose -f docker-compose.dev.yml down"
